#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعداد نظام YouTube المتقدم مع الأولوية والموافقة
"""

import os
import sys
import asyncio
from typing import Dict, List

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.video_approval_system import VideoApprovalSystem
from modules.database import db
from config.settings import BotConfig

def print_header():
    """طباعة رأس البرنامج"""
    print("🎥" + "=" * 78 + "🎥")
    print("🚀 إعداد نظام YouTube المتقدم مع الأولوية والموافقة 🚀")
    print("🎥" + "=" * 78 + "🎥")
    print()

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    requirements = {
        'YouTube API Key': bool(BotConfig.YOUTUBE_API_KEY if hasattr(BotConfig, 'YOUTUBE_API_KEY') else False),
        'Telegram Bot Token': bool(BotConfig.TELEGRAM_BOT_TOKEN),
        'Whisper API URL': 'nanami34-ai55.hf.space' in str(getattr(BotConfig, 'WHISPER_API_URL', '')),
        'HuggingFace Token': bool(getattr(BotConfig, 'HF_TOKEN', ''))
    }
    
    all_good = True
    for req, status in requirements.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {req}")
        if not status:
            all_good = False
    
    if not all_good:
        print("\n⚠️ بعض المتطلبات مفقودة. يرجى التحقق من الإعدادات.")
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    return True

def display_channel_configuration():
    """عرض تكوين القنوات"""
    print("\n📺 تكوين القنوات المحددة:")
    print("-" * 50)
    
    channels = [
        {
            'name': 'Abu Reviews',
            'url': 'https://youtube.com/@abureviews',
            'language': 'عربي',
            'priority': 1,
            'description': 'قناة مراجعات الألعاب العربية'
        },
        {
            'name': 'Faisella',
            'url': 'https://youtube.com/@faisella',
            'language': 'عربي',
            'priority': 2,
            'description': 'قناة أخبار وتحليلات الألعاب'
        },
        {
            'name': 'Nasser Gamer Zone',
            'url': 'https://youtube.com/@nassergamerzone',
            'language': 'عربي',
            'priority': 3,
            'description': 'منطقة الألعاب مع ناصر'
        },
        {
            'name': 'Gaming Channel',
            'url': 'UCTu5mqtMgH99jp-O8yLNdfg',
            'language': 'عربي',
            'priority': 4,
            'description': 'قناة ألعاب متنوعة'
        },
        {
            'name': 'JorRaptor',
            'url': 'https://youtube.com/@jorraptor',
            'language': 'إنجليزي',
            'priority': 5,
            'description': 'قناة أخبار الألعاب الأجنبية'
        }
    ]
    
    for i, channel in enumerate(channels, 1):
        print(f"{i}. 📺 {channel['name']}")
        print(f"   🔗 {channel['url']}")
        print(f"   🌐 اللغة: {channel['language']}")
        print(f"   ⭐ الأولوية: {channel['priority']}")
        print(f"   📝 {channel['description']}")
        print()

def display_workflow():
    """عرض سير العمل"""
    print("🔄 سير العمل الجديد:")
    print("-" * 30)
    
    steps = [
        "🎯 البحث في القنوات المحددة بالأولوية",
        "📹 اختيار أحدث فيديو مناسب (أقل من 25 دقيقة، أحدث من شهرين)",
        "🔍 فحص العنوان والوصف للتأكد من أنه عن الألعاب",
        "📤 إرسال طلب موافقة عبر Telegram مع تفاصيل الفيديو",
        "⏳ انتظار موافقة المدير (5 دقائق كحد أقصى)",
        "🎤 استخراج النص من الفيديو باستخدام Whisper",
        "🧠 تحليل النص للبحث عن أخبار الألعاب",
        "📰 استخراج الأخبار الرئيسية والمعلومات الإضافية",
        "✍️ توليد مقالات من الأخبار المستخرجة",
        "📤 نشر المقالات على Blogger و Telegram",
        "💾 حفظ البيانات والإحصائيات"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"{i:2d}. {step}")
    
    print()

def display_features():
    """عرض الميزات الجديدة"""
    print("✨ الميزات الجديدة:")
    print("-" * 20)
    
    features = [
        "🎯 أولوية للقنوات المحددة على المصادر الأخرى",
        "🤖 استخراج النص بالذكاء الاصطناعي (Whisper)",
        "📱 نظام موافقة تفاعلي عبر Telegram",
        "🔍 تحليل ذكي للنصوص لاستخراج الأخبار",
        "📊 تتبع شامل للفيديوهات المعالجة",
        "⏰ فلترة الفيديوهات حسب المدة والتاريخ",
        "🌐 دعم القنوات العربية والأجنبية",
        "💾 حفظ تفصيلي للبيانات والإحصائيات",
        "🔄 نظام احتياطي للمصادر التقليدية",
        "📈 تقارير أداء القنوات"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print()

async def test_system():
    """اختبار سريع للنظام"""
    print("🧪 اختبار سريع للنظام...")
    
    try:
        # اختبار محلل YouTube
        youtube_analyzer = AdvancedYouTubeAnalyzer()
        print("   ✅ تم تهيئة محلل YouTube المتقدم")
        
        # اختبار نظام الموافقة
        approval_system = VideoApprovalSystem()
        print("   ✅ تم تهيئة نظام الموافقة")
        
        # اختبار قاعدة البيانات
        stats = db.get_stats_summary(1)
        print("   ✅ قاعدة البيانات تعمل بشكل صحيح")
        
        print("✅ جميع المكونات تعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {e}")
        return False

def display_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("📖 تعليمات الاستخدام:")
    print("-" * 25)
    
    instructions = [
        "1. تأكد من تشغيل البوت الرئيسي: python main.py",
        "2. سيبحث البوت أولاً في قنوات YouTube المحددة",
        "3. عند العثور على فيديو مناسب، ستصلك رسالة موافقة على Telegram",
        "4. اضغط على ✅ للموافقة أو ❌ للرفض أو 🔄 لاختيار فيديو آخر",
        "5. سيتم استخراج النص وتحليله تلقائياً بعد الموافقة",
        "6. ستتم معالجة الأخبار ونشرها على المدونة والقناة",
        "7. يمكنك مراجعة الإحصائيات في قاعدة البيانات"
    ]
    
    for instruction in instructions:
        print(f"   {instruction}")
    
    print()

def display_telegram_setup():
    """عرض إعداد Telegram"""
    print("📱 إعداد Telegram للموافقة:")
    print("-" * 35)
    
    print("   1. تأكد من أن البوت يمكنه إرسال رسائل لك")
    print("   2. معرف القناة/المجموعة المحدد في الإعدادات:")
    print(f"      {BotConfig.TELEGRAM_CHANNEL_ID}")
    print("   3. ستصلك رسائل الموافقة مع أزرار تفاعلية")
    print("   4. يمكنك الرد خلال 5 دقائق، وإلا ستتم الموافقة تلقائياً")
    print()

async def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ يرجى إكمال الإعدادات المطلوبة أولاً")
        return
    
    # عرض التكوين
    display_channel_configuration()
    display_workflow()
    display_features()
    display_telegram_setup()
    display_usage_instructions()
    
    # اختبار النظام
    print("🧪 هل تريد إجراء اختبار سريع للنظام؟ (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes', 'نعم', 'ن']:
            success = await test_system()
            if success:
                print("\n🎉 النظام جاهز للاستخدام!")
            else:
                print("\n⚠️ هناك مشاكل تحتاج لحل")
        else:
            print("\n✅ تم تخطي الاختبار")
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الإعداد")
        return
    
    print("\n" + "🎥" + "=" * 78 + "🎥")
    print("✅ اكتمل إعداد نظام YouTube المتقدم!")
    print("🚀 يمكنك الآن تشغيل البوت الرئيسي: python main.py")
    print("🎥" + "=" * 78 + "🎥")

if __name__ == "__main__":
    asyncio.run(main())
