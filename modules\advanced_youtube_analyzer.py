# محلل يوتيوب المتقدم مع دعم Whisper وتحليل القنوات المحددة
import requests
import json
import re
import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse, parse_qs, quote
from .logger import logger
from .database import db
from config.settings import google_api_manager, BotConfig

class AdvancedYouTubeAnalyzer:
    """محلل يوتيوب متقدم مع استخراج النصوص بـ Whisper والبحث في القنوات المحددة"""
    
    def __init__(self):
        self.base_url = "https://www.googleapis.com/youtube/v3"
        self.session = requests.Session()
        
        # إعدادات Whisper API
        self.whisper_api_url = "https://nanami34-ai55.hf.space/api/transcribe"
        self.whisper_api_key = "whisper-hf-spaces-2025"
        self.hf_token = "*************************************"
        
        # القنوات المحددة بالأولوية
        self.priority_channels = [
            {
                'url': 'https://youtube.com/@abureviews',
                'name': 'Abu Reviews',
                'language': 'ar',
                'priority': 1,
                'id': None  # سيتم استخراجه
            },
            {
                'url': 'https://youtube.com/@faisella',
                'name': 'Faisella',
                'language': 'ar',
                'priority': 2,
                'id': None
            },
            {
                'url': 'https://youtube.com/@nassergamerzone',
                'name': 'Nasser Gamer Zone',
                'language': 'ar',
                'priority': 3,
                'id': None
            },
            {
                'url': 'UCTu5mqtMgH99jp-O8yLNdfg',
                'name': 'Gaming Channel',
                'language': 'ar',
                'priority': 4,
                'id': 'UCTu5mqtMgH99jp-O8yLNdfg'
            },
            {
                'url': 'https://youtube.com/@jorraptor',
                'name': 'JorRaptor',
                'language': 'en',
                'priority': 5,
                'id': None
            }
        ]
        
        # كلمات مفتاحية للتعرف على أخبار الألعاب
        self.gaming_keywords = {
            'ar': [
                'أخبار', 'خبر', 'جديد', 'تحديث', 'إصدار', 'لعبة', 'ألعاب', 'تسريب', 'إعلان',
                'مراجعة', 'تقييم', 'أفضل', 'قائمة', 'توقعات', 'معاينة', 'عرض', 'تريلر',
                'شركة', 'استوديو', 'منصة', 'كونسول', 'بلايستيشن', 'إكسبوكس', 'نينتندو'
            ],
            'en': [
                'news', 'update', 'release', 'game', 'gaming', 'leak', 'announcement',
                'review', 'preview', 'trailer', 'gameplay', 'best', 'top', 'new',
                'company', 'studio', 'platform', 'console', 'playstation', 'xbox', 'nintendo'
            ]
        }
        
        # حد أقصى لمدة الفيديو (25 دقيقة)
        self.max_video_duration = 25 * 60  # بالثواني
        
        # حد أقصى لعمر الفيديو (شهرين)
        self.max_video_age_days = 60
        
        # تهيئة معرفات القنوات
        self._initialize_channel_ids()
    
    def _initialize_channel_ids(self):
        """استخراج معرفات القنوات من الروابط"""
        for channel in self.priority_channels:
            if not channel['id'] and channel['url'].startswith('https://youtube.com/@'):
                try:
                    channel_id = self._extract_channel_id_from_handle(channel['url'])
                    if channel_id:
                        channel['id'] = channel_id
                        logger.info(f"✅ تم استخراج معرف القناة: {channel['name']} -> {channel_id}")
                except Exception as e:
                    logger.warning(f"⚠️ فشل في استخراج معرف القناة {channel['name']}: {e}")
    
    def _extract_channel_id_from_handle(self, channel_url: str) -> Optional[str]:
        """استخراج معرف القناة من رابط @handle"""
        try:
            # استخراج handle من الرابط
            handle = channel_url.split('@')[-1].split('?')[0]
            
            if not google_api_manager:
                logger.warning("⚠️ لا يمكن استخراج معرف القناة بدون Google API")
                return None
            
            api_key = google_api_manager.get_key()
            
            # البحث عن القناة باستخدام handle
            search_url = f"{self.base_url}/search"
            params = {
                'part': 'snippet',
                'q': handle,
                'type': 'channel',
                'key': api_key,
                'maxResults': 1
            }
            
            response = self.session.get(search_url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('items'):
                    return data['items'][0]['snippet']['channelId']
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في استخراج معرف القناة: {e}")
            return None
    
    async def find_latest_gaming_video(self) -> Optional[Dict]:
        """البحث عن أحدث فيديو ألعاب من القنوات المحددة"""
        try:
            logger.info("🔍 بدء البحث عن أحدث فيديو ألعاب من القنوات المحددة...")
            
            # ترتيب القنوات حسب الأولوية
            sorted_channels = sorted(self.priority_channels, key=lambda x: x['priority'])
            
            for channel in sorted_channels:
                if not channel['id']:
                    logger.warning(f"⚠️ تخطي القناة {channel['name']} - لا يوجد معرف")
                    continue
                
                logger.info(f"🔍 البحث في قناة: {channel['name']}")
                
                # الحصول على أحدث فيديوهات القناة
                videos = await self._get_channel_latest_videos(channel['id'], channel['language'])
                
                if not videos:
                    logger.info(f"📭 لا توجد فيديوهات مناسبة في قناة {channel['name']}")
                    continue
                
                # البحث عن أول فيديو مناسب
                for video in videos:
                    if await self._is_suitable_gaming_video(video, channel['language']):
                        logger.info(f"✅ تم العثور على فيديو مناسب: {video['title']}")
                        video['channel_info'] = channel
                        return video
                
                logger.info(f"⏭️ لم يتم العثور على فيديو مناسب في قناة {channel['name']}")
            
            logger.warning("⚠️ لم يتم العثور على أي فيديو مناسب في جميع القنوات")
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن فيديو الألعاب: {e}")
            return None
    
    async def _get_channel_latest_videos(self, channel_id: str, language: str) -> List[Dict]:
        """الحصول على أحدث فيديوهات القناة"""
        try:
            if not google_api_manager:
                logger.warning("⚠️ لا يمكن الحصول على فيديوهات القناة بدون Google API")
                return []
            
            api_key = google_api_manager.get_key()
            
            # الحصول على قائمة تشغيل الرفع للقناة
            channel_url = f"{self.base_url}/channels"
            channel_params = {
                'part': 'contentDetails',
                'id': channel_id,
                'key': api_key
            }
            
            channel_response = self.session.get(channel_url, params=channel_params)
            if channel_response.status_code != 200:
                logger.error(f"❌ فشل في الحصول على معلومات القناة: {channel_response.status_code}")
                return []
            
            channel_data = channel_response.json()
            if not channel_data.get('items'):
                logger.warning("⚠️ لا توجد معلومات للقناة")
                return []
            
            uploads_playlist_id = channel_data['items'][0]['contentDetails']['relatedPlaylists']['uploads']
            
            # الحصول على أحدث فيديوهات من قائمة الرفع
            playlist_url = f"{self.base_url}/playlistItems"
            playlist_params = {
                'part': 'snippet',
                'playlistId': uploads_playlist_id,
                'key': api_key,
                'maxResults': 20,  # أحدث 20 فيديو
                'order': 'date'
            }
            
            playlist_response = self.session.get(playlist_url, params=playlist_params)
            if playlist_response.status_code != 200:
                logger.error(f"❌ فشل في الحصول على فيديوهات القناة: {playlist_response.status_code}")
                return []
            
            playlist_data = playlist_response.json()
            videos = []
            
            for item in playlist_data.get('items', []):
                video_data = {
                    'id': item['snippet']['resourceId']['videoId'],
                    'title': item['snippet']['title'],
                    'description': item['snippet']['description'],
                    'published_at': item['snippet']['publishedAt'],
                    'thumbnail': item['snippet']['thumbnails'].get('high', {}).get('url', ''),
                    'channel_id': channel_id
                }
                
                # فحص عمر الفيديو
                published_date = datetime.fromisoformat(video_data['published_at'].replace('Z', '+00:00'))
                days_old = (datetime.now(published_date.tzinfo) - published_date).days
                
                if days_old <= self.max_video_age_days:
                    videos.append(video_data)
            
            logger.info(f"📹 تم العثور على {len(videos)} فيديو حديث في القناة")
            return videos
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على فيديوهات القناة: {e}")
            return []
    
    async def _is_suitable_gaming_video(self, video: Dict, language: str) -> bool:
        """فحص ما إذا كان الفيديو مناسب لأخبار الألعاب"""
        try:
            # فحص العنوان والوصف للكلمات المفتاحية
            title = video['title'].lower()
            description = video['description'].lower()
            text_to_check = f"{title} {description}"
            
            # فحص وجود كلمات مفتاحية للألعاب
            keywords = self.gaming_keywords.get(language, []) + self.gaming_keywords.get('en', [])
            has_gaming_keywords = any(keyword.lower() in text_to_check for keyword in keywords)
            
            if not has_gaming_keywords:
                logger.debug(f"⏭️ تخطي فيديو - لا يحتوي على كلمات ألعاب: {video['title']}")
                return False
            
            # فحص مدة الفيديو
            duration = await self._get_video_duration(video['id'])
            if duration and duration > self.max_video_duration:
                logger.debug(f"⏭️ تخطي فيديو - مدة طويلة ({duration//60} دقيقة): {video['title']}")
                return False
            
            # فحص ما إذا كان الفيديو تم معالجته من قبل
            if db.is_video_processed(video['id']):
                logger.debug(f"⏭️ تخطي فيديو - تم معالجته من قبل: {video['title']}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص مناسبة الفيديو: {e}")
            return False
    
    async def _get_video_duration(self, video_id: str) -> Optional[int]:
        """الحصول على مدة الفيديو بالثواني"""
        try:
            if not google_api_manager:
                return None
            
            api_key = google_api_manager.get_key()
            
            video_url = f"{self.base_url}/videos"
            params = {
                'part': 'contentDetails',
                'id': video_id,
                'key': api_key
            }
            
            response = self.session.get(video_url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('items'):
                    duration_str = data['items'][0]['contentDetails']['duration']
                    return self._parse_duration(duration_str)
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على مدة الفيديو: {e}")
            return None
    
    def _parse_duration(self, duration_str: str) -> int:
        """تحويل مدة YouTube (PT1H2M3S) إلى ثواني"""
        try:
            # إزالة PT من البداية
            duration_str = duration_str.replace('PT', '')
            
            hours = 0
            minutes = 0
            seconds = 0
            
            # استخراج الساعات
            if 'H' in duration_str:
                hours = int(duration_str.split('H')[0])
                duration_str = duration_str.split('H')[1]
            
            # استخراج الدقائق
            if 'M' in duration_str:
                minutes = int(duration_str.split('M')[0])
                duration_str = duration_str.split('M')[1]
            
            # استخراج الثواني
            if 'S' in duration_str:
                seconds = int(duration_str.split('S')[0])
            
            return hours * 3600 + minutes * 60 + seconds
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل مدة الفيديو: {e}")
            return 0

    async def extract_video_transcript_with_whisper(self, video_id: str) -> Optional[str]:
        """استخراج النص من الفيديو باستخدام Whisper"""
        try:
            logger.info(f"🎤 بدء استخراج النص من الفيديو: {video_id}")

            # تحميل الصوت من الفيديو
            audio_url = await self._get_video_audio_url(video_id)
            if not audio_url:
                logger.error("❌ فشل في الحصول على رابط الصوت")
                return None

            # إرسال الصوت إلى Whisper API
            transcript = await self._send_to_whisper_api(audio_url, video_id)

            if transcript:
                logger.info(f"✅ تم استخراج النص بنجاح - {len(transcript)} حرف")
                return transcript
            else:
                logger.error("❌ فشل في استخراج النص من Whisper")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج النص: {e}")
            return None

    async def _get_video_audio_url(self, video_id: str) -> Optional[str]:
        """الحصول على رابط الصوت من الفيديو"""
        try:
            # استخدام yt-dlp أو طريقة أخرى لاستخراج الصوت
            # هذا مثال مبسط - قد تحتاج لتطبيق أكثر تعقيداً
            video_url = f"https://www.youtube.com/watch?v={video_id}"

            # يمكن استخدام مكتبة yt-dlp هنا
            # أو استخدام خدمة خارجية لتحويل الفيديو إلى صوت

            return video_url  # مؤقتاً - سيتم تحسينه

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على رابط الصوت: {e}")
            return None

    async def _send_to_whisper_api(self, audio_source: str, video_id: str) -> Optional[str]:
        """إرسال الصوت إلى Whisper API"""
        try:
            async with aiohttp.ClientSession() as session:
                # إعداد البيانات للإرسال
                headers = {
                    'Authorization': f'Bearer {self.hf_token}',
                    'X-API-Key': self.whisper_api_key
                }

                # إذا كان المصدر رابط فيديو، نحتاج لتحميل الصوت أولاً
                if audio_source.startswith('http'):
                    # تحميل الصوت من الفيديو (مبسط)
                    audio_data = await self._download_audio_from_video(audio_source, session)
                    if not audio_data:
                        return None

                    # إرسال الصوت إلى Whisper
                    data = aiohttp.FormData()
                    data.add_field('file', audio_data, filename=f'{video_id}.mp3', content_type='audio/mpeg')

                    async with session.post(self.whisper_api_url, headers=headers, data=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result.get('text', '')
                        else:
                            logger.error(f"❌ خطأ في Whisper API: {response.status}")
                            return None

                return None

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال الصوت إلى Whisper: {e}")
            return None

    async def _download_audio_from_video(self, video_url: str, session: aiohttp.ClientSession) -> Optional[bytes]:
        """تحميل الصوت من الفيديو (مبسط)"""
        try:
            # هذا مثال مبسط - في التطبيق الحقيقي ستحتاج لاستخدام yt-dlp
            # أو خدمة تحويل خارجية

            # مؤقتاً سنعيد None ونحتاج لتطوير هذا الجزء
            logger.warning("⚠️ تحميل الصوت من الفيديو غير مطبق بعد")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في تحميل الصوت: {e}")
            return None

    def analyze_transcript_for_gaming_news(self, transcript: str, language: str = 'ar') -> Dict:
        """تحليل النص المستخرج للبحث عن أخبار الألعاب"""
        try:
            logger.info("🔍 بدء تحليل النص للبحث عن أخبار الألعاب...")

            # تقسيم النص إلى جمل
            sentences = self._split_into_sentences(transcript, language)

            # تصنيف الجمل
            gaming_news = []
            additional_info = []

            for sentence in sentences:
                classification = self._classify_sentence(sentence, language)

                if classification['type'] == 'news':
                    gaming_news.append({
                        'text': sentence,
                        'importance': classification['importance'],
                        'topics': classification['topics']
                    })
                elif classification['type'] == 'info':
                    additional_info.append({
                        'text': sentence,
                        'category': classification['category'],
                        'relevance': classification['relevance']
                    })

            # ترتيب الأخبار حسب الأهمية
            gaming_news.sort(key=lambda x: x['importance'], reverse=True)

            result = {
                'main_news': gaming_news[:5],  # أهم 5 أخبار
                'additional_info': additional_info,
                'total_sentences': len(sentences),
                'news_count': len(gaming_news),
                'info_count': len(additional_info)
            }

            logger.info(f"✅ تم تحليل النص: {result['news_count']} خبر، {result['info_count']} معلومة إضافية")
            return result

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل النص: {e}")
            return {'main_news': [], 'additional_info': [], 'total_sentences': 0, 'news_count': 0, 'info_count': 0}

    def _split_into_sentences(self, text: str, language: str) -> List[str]:
        """تقسيم النص إلى جمل"""
        try:
            # تنظيف النص
            text = re.sub(r'\s+', ' ', text.strip())

            # تقسيم حسب اللغة
            if language == 'ar':
                # للعربية
                sentences = re.split(r'[.!?؟।]', text)
            else:
                # للإنجليزية
                sentences = re.split(r'[.!?]', text)

            # تنظيف الجمل
            cleaned_sentences = []
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 10:  # تجاهل الجمل القصيرة جداً
                    cleaned_sentences.append(sentence)

            return cleaned_sentences

        except Exception as e:
            logger.error(f"❌ خطأ في تقسيم النص: {e}")
            return []

    def _classify_sentence(self, sentence: str, language: str) -> Dict:
        """تصنيف الجملة (خبر أم معلومة إضافية)"""
        try:
            sentence_lower = sentence.lower()

            # كلمات دالة على الأخبار
            news_indicators = {
                'ar': ['أعلن', 'كشف', 'أصدر', 'سيصدر', 'جديد', 'تحديث', 'إطلاق', 'تسريب', 'شائعة'],
                'en': ['announced', 'revealed', 'released', 'will release', 'new', 'update', 'launch', 'leak', 'rumor']
            }

            # كلمات دالة على المعلومات الإضافية
            info_indicators = {
                'ar': ['يمكن', 'ممكن', 'نصيحة', 'طريقة', 'كيفية', 'مراجعة', 'تقييم'],
                'en': ['can', 'possible', 'tip', 'how to', 'review', 'rating', 'opinion']
            }

            # فحص نوع الجملة
            news_score = sum(1 for indicator in news_indicators.get(language, []) if indicator in sentence_lower)
            info_score = sum(1 for indicator in info_indicators.get(language, []) if indicator in sentence_lower)

            # تحديد النوع
            if news_score > info_score:
                sentence_type = 'news'
                importance = min(news_score * 20, 100)
            else:
                sentence_type = 'info'
                importance = min(info_score * 15, 100)

            # استخراج المواضيع
            topics = self._extract_topics(sentence, language)

            return {
                'type': sentence_type,
                'importance': importance,
                'topics': topics,
                'category': self._categorize_content(sentence, language),
                'relevance': min(len(topics) * 25, 100)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في تصنيف الجملة: {e}")
            return {'type': 'info', 'importance': 0, 'topics': [], 'category': 'general', 'relevance': 0}

    def _extract_topics(self, sentence: str, language: str) -> List[str]:
        """استخراج المواضيع من الجملة"""
        try:
            topics = []
            sentence_lower = sentence.lower()

            # مواضيع الألعاب الشائعة
            game_topics = {
                'ar': {
                    'أسماء ألعاب': ['فورتنايت', 'ماين كرافت', 'كول أوف ديوتي', 'فيفا', 'جراند ثفت أوتو'],
                    'منصات': ['بلايستيشن', 'إكسبوكس', 'نينتندو', 'بي سي', 'موبايل'],
                    'شركات': ['سوني', 'مايكروسوفت', 'نينتندو', 'إيه إيه', 'يوبيسوفت']
                },
                'en': {
                    'game_names': ['fortnite', 'minecraft', 'call of duty', 'fifa', 'grand theft auto'],
                    'platforms': ['playstation', 'xbox', 'nintendo', 'pc', 'mobile'],
                    'companies': ['sony', 'microsoft', 'nintendo', 'ea', 'ubisoft']
                }
            }

            # البحث عن المواضيع
            for category, topic_list in game_topics.get(language, {}).items():
                for topic in topic_list:
                    if topic in sentence_lower:
                        topics.append(topic)

            return list(set(topics))  # إزالة التكرار

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج المواضيع: {e}")
            return []

    def _categorize_content(self, sentence: str, language: str) -> str:
        """تصنيف المحتوى إلى فئات"""
        try:
            sentence_lower = sentence.lower()

            categories = {
                'ar': {
                    'أخبار': ['أعلن', 'كشف', 'أصدر'],
                    'مراجعات': ['مراجعة', 'تقييم', 'رأي'],
                    'نصائح': ['نصيحة', 'طريقة', 'كيفية'],
                    'تسريبات': ['تسريب', 'شائعة', 'مصدر']
                },
                'en': {
                    'news': ['announced', 'revealed', 'released'],
                    'reviews': ['review', 'rating', 'opinion'],
                    'tips': ['tip', 'how to', 'guide'],
                    'leaks': ['leak', 'rumor', 'source']
                }
            }

            for category, keywords in categories.get(language, {}).items():
                if any(keyword in sentence_lower for keyword in keywords):
                    return category

            return 'general'

        except Exception as e:
            logger.error(f"❌ خطأ في تصنيف المحتوى: {e}")
            return 'general'
